import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, IntVar, BooleanVar, Text
import threading
import time
import random
from typing import List, Dict, Any, Optional
import os
from datetime import datetime
import json

from .配置管理 import 配置管理器
from .Cookie管理 import Cookie管理器
from .数据采集 import 数据采集器
from .数据处理 import 数据处理器
from .ai_提纯 import AI提纯器

class 闲鱼采集界面:
    def __init__(self, root):
        self.root = root
        self.root.title("闲鱼卖家商品采集工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 设置最小窗口大小，避免布局错乱
        self.root.minsize(800, 600)
        
        # 初始化变量
        self.正在调整布局 = False
        self.调整布局计时器ID = None
        self.上次窗口大小 = (1000, 700)
        self.主程序 = None  # 主程序实例的引用，将在创建界面函数中设置
        
        # 创建样式
        self.创建样式()
        
        # 延迟初始化这些组件，等主程序设置好配置目录后再初始化
        self.配置 = None
        self.cookie管理 = None
        self.数据采集 = None
        self.数据处理 = None
        self.ai提纯器 = None
        
        self.默认设置 = {}
        self.卖家列表 = []
        
        self.当前卖家ID = tk.StringVar(value=self.默认设置.get("默认卖家id", ""))
        self.当前卖家名称 = tk.StringVar(value=self.默认设置.get("默认卖家名称", ""))
        self.当前分组ID = tk.StringVar(value=self.默认设置.get("默认分组id", ""))
        self.当前分组名称 = tk.StringVar(value=self.默认设置.get("默认分组名称", ""))
        
        self.爬取状态 = False
        self.爬取线程 = None
        
        self.创建界面()
        
    def 创建样式(self):
        """创建界面样式"""
        style = ttk.Style()
        
        # 创建交替行的样式
        style.configure('EvenRow.TFrame', background='#f0f0f0')
        style.configure('OddRow.TFrame', background='#ffffff')
        
        # 创建按钮样式
        style.configure('采集.TButton', foreground='blue')
        style.configure('删除.TButton', foreground='red')
        
    def 创建界面(self):
        # self.创建菜单栏()  # 隐藏菜单栏

        # 创建主框架
        主框架 = ttk.Frame(self.root, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.选项卡 = ttk.Notebook(主框架)
        self.选项卡.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡页面
        self.采集页面 = ttk.Frame(self.选项卡)
        self.数据页面 = ttk.Frame(self.选项卡)
        self.提纯页面 = ttk.Frame(self.选项卡)
        self.设置页面 = ttk.Frame(self.选项卡)
        self.新上架页面 = ttk.Frame(self.选项卡)  # 新增新上架商品页面
        
        self.选项卡.add(self.采集页面, text="数据采集")
        self.选项卡.add(self.数据页面, text="数据查看")
        self.选项卡.add(self.提纯页面, text="AI提纯")
        self.选项卡.add(self.设置页面, text="系统设置")
        self.选项卡.add(self.新上架页面, text="新上架商品")  # 添加新上架商品标签页
        
        # 创建各个页面的内容
        self.创建采集页面()
        self.创建数据页面()
        self.创建提纯页面()
        self.创建设置页面()
        self.创建新上架页面()  # 创建新上架商品页面内容
        
        # 绑定标签页切换事件
        self.选项卡.bind("<<NotebookTabChanged>>", self.标签页切换)
    
    def 创建菜单栏(self):
        菜单栏 = tk.Menu(self.root)
        self.root.config(menu=菜单栏)
        
        文件菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="文件", menu=文件菜单)
        文件菜单.add_command(label="刷新Cookie", command=self.刷新Cookie)
        文件菜单.add_separator()
        文件菜单.add_command(label="退出", command=self.root.quit)
        
        卖家菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="卖家管理", menu=卖家菜单)
        卖家菜单.add_command(label="添加卖家", command=self.打开添加卖家窗口)
        卖家菜单.add_command(label="删除卖家", command=self.删除当前卖家)
        卖家菜单.add_command(label="设为默认", command=self.设置为默认卖家)
        
        帮助菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="帮助", menu=帮助菜单)
        帮助菜单.add_command(label="关于", command=self.显示关于信息)
        
    def 创建采集页面(self):
        """创建数据采集页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.采集页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建卖家管理框架
        卖家框架 = ttk.LabelFrame(主框架, text="卖家管理", padding="10")
        卖家框架.pack(fill=tk.BOTH, expand=False, pady=10)
        
        # 使用Treeview显示卖家列表
        列表框架 = ttk.Frame(卖家框架)
        列表框架.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建Treeview和滚动条
        滚动条 = ttk.Scrollbar(列表框架)
        滚动条.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.卖家列表视图 = ttk.Treeview(列表框架, columns=("卖家名称", "卖家ID"), show="headings", height=6)
        self.卖家列表视图.pack(fill=tk.BOTH, expand=True)
        
        # 设置列宽和标题
        self.卖家列表视图.column("卖家名称", width=250, anchor=tk.W)
        self.卖家列表视图.column("卖家ID", width=150, anchor=tk.W)
        
        self.卖家列表视图.heading("卖家名称", text="卖家名称")
        self.卖家列表视图.heading("卖家ID", text="卖家ID")
        
        # 设置滚动条
        滚动条.config(command=self.卖家列表视图.yview)
        self.卖家列表视图.config(yscrollcommand=滚动条.set)
        
        # 添加操作按钮
        按钮框架 = ttk.Frame(列表框架)
        按钮框架.pack(fill=tk.X, pady=5)
        
        # 添加卖家区域
        添加卖家框架 = ttk.Frame(卖家框架)
        添加卖家框架.pack(fill=tk.X, pady=10)
        
        ttk.Label(添加卖家框架, text="卖家ID:").grid(row=0, column=0, padx=5, pady=5)
        self.卖家ID输入 = ttk.Entry(添加卖家框架, width=15)
        self.卖家ID输入.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(添加卖家框架, text="卖家名称:").grid(row=0, column=2, padx=5, pady=5)
        self.卖家名称输入 = ttk.Entry(添加卖家框架, width=15)
        self.卖家名称输入.grid(row=0, column=3, padx=5, pady=5)
        
        添加按钮 = ttk.Button(添加卖家框架, text="添加卖家", command=self.添加卖家)
        添加按钮.grid(row=0, column=4, padx=10, pady=5)
        
        # 创建日志区域
        日志框架 = ttk.LabelFrame(主框架, text="运行日志", padding="10")
        日志框架.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.日志文本框 = scrolledtext.ScrolledText(日志框架, wrap=tk.WORD, height=10)
        self.日志文本框.pack(fill=tk.BOTH, expand=True)
        self.日志文本框.config(state=tk.DISABLED)
        
        # 创建操作按钮区域
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        self.一键采集按钮 = ttk.Button(按钮框架, text="一键采集全部卖家", command=self.一键采集全部卖家)
        self.一键采集按钮.pack(side=tk.LEFT, padx=5)
        
        self.开始按钮 = ttk.Button(按钮框架, text="采集选中卖家", command=self.开始批量采集)
        self.开始按钮.pack(side=tk.LEFT, padx=5)
        
        self.停止按钮 = ttk.Button(按钮框架, text="停止采集", command=self.停止采集, state=tk.DISABLED)
        self.停止按钮.pack(side=tk.LEFT, padx=5)
        
        清空日志按钮 = ttk.Button(按钮框架, text="清空日志", command=self.清空日志)
        清空日志按钮.pack(side=tk.LEFT, padx=5)
        
        打开文件夹按钮 = ttk.Button(按钮框架, text="打开输出文件夹", command=self.打开输出文件夹)
        打开文件夹按钮.pack(side=tk.RIGHT, padx=5)
        
        # 绑定卖家列表视图的事件
        self.卖家列表视图.bind("<Button-3>", self.显示卖家操作菜单)  # 绑定右键点击事件
        self.卖家列表视图.bind("<Double-1>", self.双击开始采集)  # 绑定双击事件
        
        # 初始化卖家列表（延迟到配置设置后）
        # self.刷新卖家列表()
    
    def 刷新卖家列表(self):
        """刷新卖家列表"""
        # 如果配置还未初始化，则跳过
        if self.配置 is None:
            return

        # 清空现有数据
        for item in self.卖家列表视图.get_children():
            self.卖家列表视图.delete(item)

        # 获取所有卖家
        卖家列表 = self.配置.获取所有卖家()

        # 打印调试信息
        self.写入日志(f"共加载了 {len(卖家列表)} 个卖家")

        # 检查卖家列表是否为空
        if not 卖家列表:
            self.写入日志("卖家列表为空，请添加卖家")
            return

        # 添加卖家数据到Treeview
        for 卖家 in 卖家列表:
            self.卖家列表视图.insert("", tk.END, values=(卖家['卖家名称'], 卖家['卖家ID']))
    
    def 显示卖家操作菜单(self, event):
        """显示卖家右键操作菜单"""
        # 获取当前点击的项
        item = self.卖家列表视图.identify_row(event.y)
        if not item:
            return
            
        # 选中被点击的项
        self.卖家列表视图.selection_set(item)
        
        # 获取卖家信息
        values = self.卖家列表视图.item(item, "values")
        if not values:
            return
            
        卖家名称 = values[0]
        卖家ID = values[1]
        
        # 创建右键菜单
        菜单 = tk.Menu(self.root, tearoff=0)
        菜单.add_command(label="采集", command=lambda: self.开始单个卖家采集(卖家ID))
        菜单.add_command(label="加载并查看", command=lambda: self.加载卖家查看(卖家ID, 卖家名称))
        菜单.add_command(label="保存游戏名称", command=lambda: self.加载卖家并保存游戏名称(卖家ID, 卖家名称))
        菜单.add_separator()
        菜单.add_command(label="删除", command=lambda: self.删除卖家(卖家ID, 卖家名称))
        菜单.add_command(label="设为默认", command=lambda: self.设置为默认卖家(卖家ID, 卖家名称))
        
        # 显示菜单
        菜单.tk_popup(event.x_root, event.y_root)
        
    def 加载卖家查看(self, 卖家ID, 卖家名称):
        """切换到数据查看页面并加载卖家数据"""
        # 切换到数据查看页面
        self.选项卡.select(self.数据页面)
        
        # 更新卖家下拉列表
        self.刷新卖家下拉列表()
        
        # 选择对应的卖家
        for i, 值 in enumerate(self.卖家选择['values']):
            if f"{卖家名称} (ID: {卖家ID})" in 值:
                self.卖家选择.current(i)
                self.加载卖家数据()
                break
                
    def 加载卖家并保存游戏名称(self, 卖家ID, 卖家名称):
        """加载卖家数据并保存游戏名称"""
        # 构造文件路径
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        if not os.path.exists(文件路径):
            messagebox.showinfo("提示", f"找不到卖家 {卖家名称} 的数据文件")
            return
            
        try:
            # 读取JSON文件
            with open(文件路径, 'r', encoding='utf-8') as f:
                数据 = json.load(f)
                
            # 提取游戏名称
            游戏名称列表 = []
            for 商品 in 数据.get("商品列表", []):
                游戏名称 = 商品.get("游戏名称")
                if 游戏名称 and 游戏名称.strip():
                    游戏名称列表.append(游戏名称)
                    
            if not 游戏名称列表:
                messagebox.showinfo("提示", f"卖家 {卖家名称} 的数据中没有游戏名称，请先进行AI提纯")
                return
                
            # 文件路径
            文件名 = "提纯游戏名字.txt"
            文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
            
            # 读取现有文件内容（如果存在）
            现有游戏名称 = set()
            if os.path.exists(文件路径):
                try:
                    with open(文件路径, 'r', encoding='utf-8') as f:
                        for 行 in f:
                            名称 = 行.strip()
                            if 名称:
                                现有游戏名称.add(名称)
                except Exception as e:
                    messagebox.showerror("错误", f"读取现有文件时出错: {e}")
                    return
            
            # 去重并添加新的游戏名称
            新增数量 = 0
            for 名称 in 游戏名称列表:
                if 名称 not in 现有游戏名称:
                    现有游戏名称.add(名称)
                    新增数量 += 1
            
            # 保存到文件
            with open(文件路径, 'w', encoding='utf-8') as f:
                for 名称 in sorted(现有游戏名称):  # 排序后保存
                    f.write(f"{名称}\n")
            
            总数量 = len(现有游戏名称)
            messagebox.showinfo("成功", f"游戏名称已保存到文件: {文件名}\n新增: {新增数量} 个，总计: {总数量} 个")
            
        except Exception as e:
            messagebox.showerror("错误", f"处理卖家数据时出错: {e}")
    
    def 删除卖家(self, 卖家ID, 卖家名称):
        """删除卖家"""
        if messagebox.askyesno("确认删除", f"确定要删除卖家 {卖家名称} (ID: {卖家ID}) 吗?"):
            if self.配置.删除卖家信息(卖家ID):
                messagebox.showinfo("成功", f"已删除卖家: {卖家名称}")
                self.刷新卖家列表()
                if hasattr(self, '提纯卖家选择'):
                    self.刷新提纯卖家列表()
            else:
                messagebox.showerror("错误", "删除卖家失败")
    
    def 添加卖家(self):
        """添加卖家"""
        卖家ID = self.卖家ID输入.get().strip()
        卖家名称 = self.卖家名称输入.get().strip()
        
        if not 卖家ID:
            messagebox.showerror("错误", "请输入卖家ID")
            return
            
        if not 卖家名称:
            messagebox.showerror("错误", "请输入卖家名称")
            return
            
        if self.配置.保存卖家信息(卖家ID, 卖家名称):
            messagebox.showinfo("成功", f"已添加卖家: {卖家名称} (ID: {卖家ID})")
            self.卖家ID输入.delete(0, tk.END)
            self.卖家名称输入.delete(0, tk.END)
            self.刷新卖家列表()
        else:
            messagebox.showerror("错误", "添加卖家失败")
    
    def 开始单个卖家采集(self, 卖家ID):
        """开始采集单个卖家的商品"""
        if self.爬取状态:
            messagebox.showinfo("提示", "当前已有采集任务正在运行，请等待完成后再试")
            return
        
        # 设置采集页数为0（全部页面）
        self.爬取状态 = True
        self.开始按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)
        
        self.写入日志(f"开始采集卖家 {卖家ID} 的商品数据...")
        
        # 创建一个线程来执行采集
        self.爬取线程 = threading.Thread(
            target=self.爬取任务,
            args=([卖家ID], 0, False, False)
        )
        self.爬取线程.daemon = True
        self.爬取线程.start()
    
    def 开始批量采集(self):
        """开始批量采集"""
        # 检查是否有选中的卖家
        选中项 = self.卖家列表视图.selection()
        if not 选中项:
            messagebox.showwarning("警告", "请先选择要采集的卖家")
            return
            
        # 获取选中卖家的ID
        卖家ID列表 = []
        for item in 选中项:
            卖家ID = self.卖家列表视图.item(item, "values")[1]
            卖家ID列表.append(卖家ID)
            
        # 默认参数
        页数 = 0  # 0表示采集全部页面
        只采集有想要 = False
        只保存ID = False
            
        # 更新界面状态
        self.开始按钮.config(state=tk.DISABLED)
        self.一键采集按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)
        
        # 启动爬取任务
        self.爬取线程 = threading.Thread(target=self.爬取任务, args=(卖家ID列表, 页数, 只采集有想要, 只保存ID))
        self.爬取线程.daemon = True
        self.爬取线程.start()
        
    def 一键采集全部卖家(self):
        """一键采集全部卖家"""
        # 默认参数
        页数 = 0  # 0表示采集全部页面
        只采集有想要 = False
        只保存ID = False
        
        # 更新界面状态
        self.开始按钮.config(state=tk.DISABLED)
        self.一键采集按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)
        
        # 调用主程序的一键采集全部卖家方法
        self.主程序.一键采集全部卖家(页数, 只采集有想要, 只保存ID)
        
    def 爬取任务(self, 卖家ID列表, 页数, 只采集有想要, 只保存ID):
        """执行爬取任务"""
        self.爬取状态 = True
        
        try:
            # 调用主程序的批量采集方法
            self.主程序.开始批量采集(卖家ID列表, 页数, 只采集有想要, 只保存ID)
        except Exception as e:
            self.写入日志(f"爬取过程中出现错误: {e}")
        finally:
            self.爬取状态 = False
            # 恢复按钮状态
            self.root.after(0, lambda: self.开始按钮.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.一键采集按钮.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.停止按钮.config(state=tk.DISABLED))
    
    def 停止采集(self):
        """停止采集"""
        if not self.爬取状态:
            return
            
        self.爬取状态 = False
        self.主程序.采集状态 = "空闲"  # 直接设置主程序的采集状态
        self.写入日志("正在停止采集...")
        self.开始按钮.config(state=tk.NORMAL)
        self.一键采集按钮.config(state=tk.NORMAL)
        self.停止按钮.config(state=tk.DISABLED)
    
    def 写入日志(self, 消息: str):
        """写入日志"""
        当前时间 = datetime.now().strftime("%H:%M:%S")
        日志消息 = f"[{当前时间}] {消息}\n"
        
        self.日志文本框.config(state=tk.NORMAL)
        self.日志文本框.insert(tk.END, 日志消息)
        self.日志文本框.see(tk.END)
        self.日志文本框.config(state=tk.DISABLED)
    
    def 清空日志(self):
        """清空日志"""
        self.日志文本框.config(state=tk.NORMAL)
        self.日志文本框.delete(1.0, tk.END)
        self.日志文本框.config(state=tk.DISABLED)
    
    def 打开输出文件夹(self):
        """打开输出文件夹"""
        当前目录 = os.getcwd()
        os.startfile(当前目录)

    def 创建数据页面(self):
        """创建数据查看页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.数据页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建卖家选择框架
        选择框架 = ttk.Frame(主框架)
        选择框架.pack(fill=tk.X, pady=10)
        
        ttk.Label(选择框架, text="选择卖家:").pack(side=tk.LEFT, padx=5)
        
        # 卖家下拉框
        self.卖家选择 = ttk.Combobox(选择框架, width=30, state="readonly")
        self.卖家选择.pack(side=tk.LEFT, padx=5)
        
        # 刷新卖家下拉列表
        self.刷新卖家下拉列表()
        
        # 绑定选择事件
        self.卖家选择.bind("<<ComboboxSelected>>", lambda e: self.加载卖家数据())
        
        # 创建统计信息框架
        self.统计信息框架 = ttk.LabelFrame(主框架, text="统计信息", padding="10")
        self.统计信息框架.pack(fill=tk.X, pady=10)
        
        # 创建统计信息标签
        统计信息内容框架 = ttk.Frame(self.统计信息框架)
        统计信息内容框架.pack(fill=tk.X, expand=True)
        
        # 第一行统计信息
        ttk.Label(统计信息内容框架, text="总商品数:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=2)
        self.总商品数标签 = ttk.Label(统计信息内容框架, text="0")
        self.总商品数标签.grid(row=0, column=1, sticky=tk.W, padx=10, pady=2)
        
        ttk.Label(统计信息内容框架, text="有想要商品数:").grid(row=0, column=2, sticky=tk.W, padx=10, pady=2)
        self.有想要商品数标签 = ttk.Label(统计信息内容框架, text="0")
        self.有想要商品数标签.grid(row=0, column=3, sticky=tk.W, padx=10, pady=2)
        
        ttk.Label(统计信息内容框架, text="无想要商品数:").grid(row=0, column=4, sticky=tk.W, padx=10, pady=2)
        self.无想要商品数标签 = ttk.Label(统计信息内容框架, text="0")
        self.无想要商品数标签.grid(row=0, column=5, sticky=tk.W, padx=10, pady=2)
        
        # 第二行统计信息
        ttk.Label(统计信息内容框架, text="最高想要数:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=2)
        self.最高想要数标签 = ttk.Label(统计信息内容框架, text="0")
        self.最高想要数标签.grid(row=1, column=1, sticky=tk.W, padx=10, pady=2)
        
        ttk.Label(统计信息内容框架, text="平均想要数:").grid(row=1, column=2, sticky=tk.W, padx=10, pady=2)
        self.平均想要数标签 = ttk.Label(统计信息内容框架, text="0")
        self.平均想要数标签.grid(row=1, column=3, sticky=tk.W, padx=10, pady=2)
        
        ttk.Label(统计信息内容框架, text="最近采集时间:").grid(row=1, column=4, sticky=tk.W, padx=10, pady=2)
        self.最近采集时间标签 = ttk.Label(统计信息内容框架, text="无")
        self.最近采集时间标签.grid(row=1, column=5, sticky=tk.W, padx=10, pady=2)
        
        # 第三行统计信息
        ttk.Label(统计信息内容框架, text="新上架商品数:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=2)
        self.新上架商品数标签 = ttk.Label(统计信息内容框架, text="0")
        self.新上架商品数标签.grid(row=2, column=1, sticky=tk.W, padx=10, pady=2)
        
        # 创建数据显示区域
        数据框架 = ttk.LabelFrame(主框架, text="商品数据", padding="10")
        数据框架.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表格框架 - 添加一个额外的框架来容纳表格和滚动条
        表格框架 = ttk.Frame(数据框架)
        表格框架.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        表格滚动条Y = ttk.Scrollbar(表格框架, orient=tk.VERTICAL)
        表格滚动条Y.pack(side=tk.RIGHT, fill=tk.Y)
        
        表格滚动条X = ttk.Scrollbar(数据框架, orient=tk.HORIZONTAL)
        表格滚动条X.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建表格
        self.数据表格 = ttk.Treeview(表格框架, columns=("商品ID", "商品标题", "想要人数", "游戏名称", "首次采集时间", "最近采集时间"),
                                yscrollcommand=表格滚动条Y.set, xscrollcommand=表格滚动条X.set)
        self.数据表格.pack(fill=tk.BOTH, expand=True)
        
        # 设置滚动条命令
        表格滚动条Y.config(command=self.数据表格.yview)
        表格滚动条X.config(command=self.数据表格.xview)
        
        # 设置列宽
        self.数据表格.column("#0", width=0, stretch=tk.NO)  # 隐藏第一列
        self.数据表格.column("商品ID", width=100, anchor=tk.W)
        self.数据表格.column("商品标题", width=250, anchor=tk.W)
        self.数据表格.column("想要人数", width=80, anchor=tk.CENTER)
        self.数据表格.column("游戏名称", width=150, anchor=tk.W)
        self.数据表格.column("首次采集时间", width=150, anchor=tk.CENTER)
        self.数据表格.column("最近采集时间", width=150, anchor=tk.CENTER)
        
        # 设置表头
        self.数据表格.heading("#0", text="")
        self.数据表格.heading("商品ID", text="商品ID")
        self.数据表格.heading("商品标题", text="商品标题")
        self.数据表格.heading("想要人数", text="想要人数")
        self.数据表格.heading("游戏名称", text="游戏名称")
        self.数据表格.heading("首次采集时间", text="首次采集时间")
        self.数据表格.heading("最近采集时间", text="最近采集时间")
        
        # 绑定右键点击事件
        self.数据表格.bind("<Button-3>", self.显示数据表格右键菜单)
        
        # 创建操作按钮区域
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        导出按钮 = ttk.Button(按钮框架, text="导出数据", command=self.导出数据)
        导出按钮.pack(side=tk.LEFT, padx=5)
        
        导出无想要商品ID按钮 = ttk.Button(按钮框架, text="导出低想要商品ID", command=self.导出无想要商品ID)
        导出无想要商品ID按钮.pack(side=tk.LEFT, padx=5)
        
        保存游戏名称按钮 = ttk.Button(按钮框架, text="保存游戏名称", command=self.保存游戏名称到文件)
        保存游戏名称按钮.pack(side=tk.LEFT, padx=5)
        
        打开新上架文件按钮 = ttk.Button(按钮框架, text="查看新上架游戏", command=self.打开新上架游戏文件)
        打开新上架文件按钮.pack(side=tk.LEFT, padx=5)
        
        清空按钮 = ttk.Button(按钮框架, text="清空表格", command=self.清空数据表格)
        清空按钮.pack(side=tk.LEFT, padx=5)
        
        # 初始化卖家列表
        self.刷新卖家列表()
    
    def 刷新卖家下拉列表(self):
        """刷新卖家下拉列表"""
        # 如果数据处理器还未初始化，则跳过
        if self.数据处理 is None:
            return

        卖家数据 = self.数据处理.按卖家分组数据()
        卖家选项 = []

        for 卖家ID, 信息 in 卖家数据.items():
            卖家选项.append(f"{信息['卖家名称']} (ID: {卖家ID})")

        self.卖家选择['values'] = 卖家选项
        if 卖家选项:
            self.卖家选择.current(0)

    def 创建提纯页面(self):
        """创建AI提纯页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.提纯页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建卖家选择框架
        选择框架 = ttk.Frame(主框架)
        选择框架.pack(fill=tk.X, pady=10)
        
        ttk.Label(选择框架, text="选择卖家:").pack(side=tk.LEFT, padx=5)
        
        # 卖家下拉框
        self.提纯卖家选择 = ttk.Combobox(选择框架, width=30, state="readonly")
        self.提纯卖家选择.pack(side=tk.LEFT, padx=5)
        
        # 刷新卖家下拉列表
        self.刷新提纯卖家列表()
        
        # 绑定选择事件
        self.提纯卖家选择.bind("<<ComboboxSelected>>", lambda e: self.加载提纯数据())
        
        # 加载按钮
        加载按钮 = ttk.Button(选择框架, text="加载数据", command=self.加载提纯数据)
        加载按钮.pack(side=tk.LEFT, padx=5)
        
        # 添加导入外部标题按钮
        导入标题按钮 = ttk.Button(选择框架, text="导入外部标题", command=self.导入外部商品标题)
        导入标题按钮.pack(side=tk.LEFT, padx=5)
        
        # 创建数据显示区域
        数据框架 = ttk.LabelFrame(主框架, text="商品数据", padding="10")
        数据框架.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表格框架 - 添加一个额外的框架来容纳表格和滚动条
        表格框架 = ttk.Frame(数据框架)
        表格框架.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        表格滚动条Y = ttk.Scrollbar(表格框架, orient=tk.VERTICAL)
        表格滚动条Y.pack(side=tk.RIGHT, fill=tk.Y)
        
        表格滚动条X = ttk.Scrollbar(数据框架, orient=tk.HORIZONTAL)
        表格滚动条X.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建表格
        self.提纯表格 = ttk.Treeview(表格框架, columns=("商品ID", "商品标题", "想要人数", "游戏名称"),
                                yscrollcommand=表格滚动条Y.set, xscrollcommand=表格滚动条X.set)
        self.提纯表格.pack(fill=tk.BOTH, expand=True)
        
        # 设置滚动条命令
        表格滚动条Y.config(command=self.提纯表格.yview)
        表格滚动条X.config(command=self.提纯表格.xview)
        
        # 设置列宽
        self.提纯表格.column("#0", width=0, stretch=tk.NO)  # 隐藏第一列
        self.提纯表格.column("商品ID", width=100, anchor=tk.W)
        self.提纯表格.column("商品标题", width=300, anchor=tk.W)
        self.提纯表格.column("想要人数", width=80, anchor=tk.CENTER)
        self.提纯表格.column("游戏名称", width=200, anchor=tk.W)
        
        # 设置表头
        self.提纯表格.heading("#0", text="")
        self.提纯表格.heading("商品ID", text="商品ID")
        self.提纯表格.heading("商品标题", text="商品标题")
        self.提纯表格.heading("想要人数", text="想要人数")
        self.提纯表格.heading("游戏名称", text="游戏名称")
        
        # 绑定右键点击事件
        self.提纯表格.bind("<Button-3>", self.显示提纯表格右键菜单)
        
        # 创建操作按钮区域
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        开始提纯按钮 = ttk.Button(按钮框架, text="开始AI提纯", command=self.开始AI提纯)
        开始提纯按钮.pack(side=tk.LEFT, padx=5)
        
        导出按钮 = ttk.Button(按钮框架, text="导出提纯结果", command=self.导出提纯结果)
        导出按钮.pack(side=tk.LEFT, padx=5)
        
        保存游戏名称按钮 = ttk.Button(按钮框架, text="保存游戏名称", command=self.保存提纯游戏名称到文件)
        保存游戏名称按钮.pack(side=tk.LEFT, padx=5)
        
        清空按钮 = ttk.Button(按钮框架, text="清空表格", command=self.清空提纯表格)
        清空按钮.pack(side=tk.LEFT, padx=5)
        
        # 提纯状态框架
        状态框架 = ttk.LabelFrame(主框架, text="提纯状态", padding="10")
        状态框架.pack(fill=tk.X, pady=10)
        
        self.提纯状态标签 = ttk.Label(状态框架, text="就绪")
        self.提纯状态标签.pack(fill=tk.X)
        
        self.提纯进度条 = ttk.Progressbar(状态框架, orient=tk.HORIZONTAL, mode='determinate')
        self.提纯进度条.pack(fill=tk.X, pady=5)
        
        # 初始化卖家列表
        self.刷新提纯卖家列表()
    
    def 刷新提纯卖家列表(self):
        """刷新提纯页面的卖家下拉列表"""
        # 如果数据处理器还未初始化，则跳过
        if self.数据处理 is None:
            return

        卖家数据 = self.数据处理.按卖家分组数据()
        卖家选项 = []

        for 卖家ID, 信息 in 卖家数据.items():
            卖家选项.append(f"{信息['卖家名称']} (ID: {卖家ID})")

        self.提纯卖家选择['values'] = 卖家选项
        if 卖家选项:
            self.提纯卖家选择.current(0)
    
    def 加载提纯数据(self):
        """加载所选卖家的数据到提纯表格"""
        选择项 = self.提纯卖家选择.get()
        if not 选择项:
            messagebox.showerror("错误", "请先选择一个卖家")
            return
        
        # 从选择项中提取卖家ID
        卖家ID = 选择项.split("ID: ")[1].strip(")")
        
        # 清空表格
        self.清空提纯表格()
        
        # 加载数据
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        if not os.path.exists(文件路径):
            messagebox.showerror("错误", f"找不到卖家 {卖家ID} 的数据文件")
            return
        
        数据 = self.数据处理.加载数据(文件路径)
        if "错误" in 数据:
            messagebox.showerror("错误", f"加载数据失败: {数据['错误']}")
            return
        
        # 显示卖家信息
        商品列表 = 数据.get("商品列表", [])
        
        # 按想要人数排序（如果有）
        try:
            商品列表 = sorted(商品列表, key=lambda x: x.get("想要人数", 0), reverse=True)
        except:
            pass
        
        # 填充表格
        for i, 商品 in enumerate(商品列表):
            商品ID = 商品.get("商品ID", "")
            商品标题 = 商品.get("商品标题", "")
            想要人数 = 商品.get("想要人数", "")
            游戏名称 = 商品.get("游戏名称", "")
            
            self.提纯表格.insert("", tk.END, values=(商品ID, 商品标题, 想要人数, 游戏名称))
    
    def 清空提纯表格(self):
        """清空提纯表格"""
        for item in self.提纯表格.get_children():
            self.提纯表格.delete(item)
    
    def 开始AI提纯(self):
        """开始AI提纯过程"""
        # 获取表格中的所有商品
        商品列表 = []
        for item in self.提纯表格.get_children():
            values = self.提纯表格.item(item, 'values')
            商品ID = values[0]
            商品标题 = values[1]
            想要人数 = values[2] if len(values) > 2 else ""
            
            商品列表.append({
                "商品ID": 商品ID,
                "商品标题": 商品标题,
                "想要人数": 想要人数
            })
        
        if not 商品列表:
            messagebox.showerror("错误", "表格中没有数据可以提纯")
            return
        
        # 开始提纯
        self.提纯状态标签.config(text="正在进行AI提纯...")
        self.提纯进度条["value"] = 0
        self.提纯进度条["maximum"] = len(商品列表)
        
        # 创建线程执行提纯任务
        提纯线程 = threading.Thread(target=self._提纯任务, args=(商品列表,))
        提纯线程.daemon = True
        提纯线程.start()
    
    def _提纯任务(self, 商品列表):
        """在线程中执行提纯任务"""
        提纯结果 = {}
        
        for i, 商品 in enumerate(商品列表):
            商品ID = 商品["商品ID"]
            商品标题 = 商品["商品标题"]
            
            # 更新进度
            self.root.after(0, lambda i=i: self.提纯进度条.config(value=i+1))
            self.root.after(0, lambda i=i, 标题=商品标题: self.提纯状态标签.config(text=f"正在提纯 {i+1}/{len(商品列表)}: {标题[:20]}..."))
            
            # 调用AI提纯
            try:
                游戏名称 = self.ai提纯器.提取游戏名称(商品标题)
                提纯结果[商品ID] = 游戏名称
                
                # 更新表格中的游戏名称
                for item in self.提纯表格.get_children():
                    if self.提纯表格.item(item, 'values')[0] == 商品ID:
                        values = list(self.提纯表格.item(item, 'values'))
                        values[3] = 游戏名称  # 更新游戏名称
                        self.root.after(0, lambda item=item, values=values: self.提纯表格.item(item, values=values))
                        break
            except Exception as e:
                print(f"提纯商品 {商品ID} 时出错: {e}")
                提纯结果[商品ID] = "提纯失败"
            
            # 暂停一下，避免请求过于频繁
            time.sleep(0.5)
        
        # 提纯完成
        self.root.after(0, lambda: self.提纯状态标签.config(text=f"提纯完成，共处理 {len(商品列表)} 个商品"))
        
        # 如果是从卖家数据加载的，则保存结果到JSON
        选择项 = self.提纯卖家选择.get()
        if 选择项 and not 商品列表[0]["商品ID"].startswith("外部导入"):
            卖家ID = 选择项.split("ID: ")[1].strip(")")
            self.root.after(0, lambda id=卖家ID, res=提纯结果: self.保存提纯结果到JSON(id, res))
    
    def 导出提纯结果(self):
        """导出提纯结果到CSV文件"""
        选择项 = self.提纯卖家选择.get()
        if not 选择项:
            messagebox.showerror("错误", "请先选择一个卖家")
            return
        
        # 从选择项中提取卖家ID和名称
        卖家名称 = 选择项.split(" (ID:")[0]
        卖家ID = 选择项.split("ID: ")[1].strip(")")
        
        # 获取表格数据
        数据 = []
        for item in self.提纯表格.get_children():
            values = self.提纯表格.item(item, 'values')
            数据.append(values)
        
        if not 数据:
            messagebox.showerror("错误", "表格中没有数据可导出")
            return
        
        # 导出到CSV
        try:
            导出文件名 = f"卖家_{卖家ID}_{卖家名称}_提纯结果.csv"
            with open(导出文件名, 'w', encoding='utf-8-sig', newline='') as f:
                import csv
                writer = csv.writer(f)
                writer.writerow(["商品ID", "商品标题", "想要人数", "游戏名称"])
                writer.writerows(数据)
            
            messagebox.showinfo("成功", f"提纯结果已导出到文件: {导出文件名}")
            os.startfile(os.path.dirname(os.path.abspath(导出文件名)))
        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {e}")
    
    def 创建设置页面(self):
        """创建系统设置页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.设置页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建系统设置框架
        系统设置框架 = ttk.LabelFrame(主框架, text="系统设置", padding="10")
        系统设置框架.pack(fill=tk.X, pady=10)
        
        # 加载当前设置
        if self.配置 is not None:
            系统设置 = self.配置.获取系统设置()
        else:
            # 使用默认设置
            系统设置 = {
                "用户代理": "",
                "应用密钥": "",
                "接口名称": "",
                "基础URL": ""
            }

        # 用户代理
        ttk.Label(系统设置框架, text="用户代理:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.用户代理输入 = ttk.Entry(系统设置框架, width=60)
        self.用户代理输入.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.用户代理输入.insert(0, 系统设置["用户代理"])

        # 应用密钥
        ttk.Label(系统设置框架, text="应用密钥:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.应用密钥输入 = ttk.Entry(系统设置框架, width=30)
        self.应用密钥输入.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.应用密钥输入.insert(0, 系统设置["应用密钥"])

        # 接口名称
        ttk.Label(系统设置框架, text="接口名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.接口名称输入 = ttk.Entry(系统设置框架, width=40)
        self.接口名称输入.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.接口名称输入.insert(0, 系统设置["接口名称"])

        # 基础URL
        ttk.Label(系统设置框架, text="基础URL:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.基础URL输入 = ttk.Entry(系统设置框架, width=60)
        self.基础URL输入.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        self.基础URL输入.insert(0, 系统设置["基础URL"])
        
        # Cookie管理框架
        Cookie框架 = ttk.LabelFrame(主框架, text="Cookie管理", padding="10")
        Cookie框架.pack(fill=tk.X, pady=10)
        
        # 当前Cookie
        ttk.Label(Cookie框架, text="当前Cookie:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.Cookie输入 = Text(Cookie框架, width=60, height=5)
        self.Cookie输入.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        # 如果cookie管理器已初始化，则插入当前cookie
        if self.cookie管理 is not None:
            self.Cookie输入.insert("1.0", self.cookie管理.cookie)
        
        # 按钮框架
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        保存设置按钮 = ttk.Button(按钮框架, text="保存系统设置", command=self.保存系统设置)
        保存设置按钮.pack(side=tk.LEFT, padx=5)
        
        保存Cookie按钮 = ttk.Button(按钮框架, text="保存Cookie", command=self.保存Cookie)
        保存Cookie按钮.pack(side=tk.LEFT, padx=5)
        
        刷新Cookie按钮 = ttk.Button(按钮框架, text="自动刷新Cookie", command=self.刷新Cookie)
        刷新Cookie按钮.pack(side=tk.LEFT, padx=5)
    
    def 保存系统设置(self):
        """保存系统设置"""
        try:
            self.配置.配置['系统设置']['用户代理'] = self.用户代理输入.get()
            self.配置.配置['系统设置']['应用密钥'] = self.应用密钥输入.get()
            self.配置.配置['系统设置']['接口名称'] = self.接口名称输入.get()
            self.配置.配置['系统设置']['基础URL'] = self.基础URL输入.get()
            
            self.配置.保存配置()
            
            # 更新数据采集器的设置
            self.数据采集.用户代理 = self.用户代理输入.get()
            self.数据采集.应用密钥 = self.应用密钥输入.get()
            self.数据采集.接口名称 = self.接口名称输入.get()
            self.数据采集.基础URL = self.基础URL输入.get()
            
            messagebox.showinfo("成功", "系统设置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存系统设置失败: {e}")

    def 刷新设置页面(self):
        """刷新设置页面的显示内容"""
        if self.配置 is not None and hasattr(self, '用户代理输入'):
            try:
                系统设置 = self.配置.获取系统设置()

                # 清空并重新填充输入框
                self.用户代理输入.delete(0, tk.END)
                self.用户代理输入.insert(0, 系统设置.get("用户代理", ""))

                self.应用密钥输入.delete(0, tk.END)
                self.应用密钥输入.insert(0, 系统设置.get("应用密钥", ""))

                self.接口名称输入.delete(0, tk.END)
                self.接口名称输入.insert(0, 系统设置.get("接口名称", ""))

                self.基础URL输入.delete(0, tk.END)
                self.基础URL输入.insert(0, 系统设置.get("基础URL", ""))

                # 刷新Cookie显示
                if hasattr(self, 'Cookie输入'):
                    cookie = self.cookie管理.获取Cookie() if self.cookie管理 else ""
                    self.Cookie输入.delete("1.0", tk.END)
                    self.Cookie输入.insert("1.0", cookie)

            except Exception as e:
                print(f"刷新设置页面失败: {e}")

    def 刷新数据页面(self):
        """刷新数据页面的显示内容"""
        if hasattr(self, '卖家选择'):
            self.刷新卖家下拉列表()

    def 刷新AI提纯页面(self):
        """刷新AI提纯页面的显示内容"""
        if hasattr(self, '提纯卖家选择'):
            self.刷新提纯卖家列表()

    def 保存Cookie(self):
        """保存Cookie"""
        try:
            cookie = self.Cookie输入.get("1.0", tk.END).strip()
            if not cookie:
                messagebox.showerror("错误", "Cookie不能为空")
                return
            
            if self.配置.保存Cookie(cookie):
                self.数据采集.cookie = cookie
                messagebox.showinfo("成功", "Cookie已保存")
            else:
                messagebox.showerror("错误", "保存Cookie失败")
        except Exception as e:
            messagebox.showerror("错误", f"保存Cookie失败: {e}")
    
    def 刷新Cookie(self):
        """刷新Cookie"""
        if messagebox.askyesno("确认", "确定要自动刷新Cookie吗？这将打开浏览器窗口。"):
            # 创建线程执行刷新，避免界面卡顿
            threading.Thread(target=self._刷新Cookie线程, daemon=True).start()
    
    def _刷新Cookie线程(self):
        """刷新Cookie的线程"""
        try:
            # 更新界面
            self.root.after(0, lambda: messagebox.showinfo("提示", "正在启动浏览器获取Cookie，请稍候..."))
            
            # 刷新Cookie
            new_cookie = self.cookie管理.刷新Cookie()
            
            if new_cookie:
                # 更新界面
                self.root.after(0, lambda: self.Cookie输入.delete("1.0", tk.END))
                self.root.after(0, lambda: self.Cookie输入.insert("1.0", new_cookie))
                self.root.after(0, lambda: messagebox.showinfo("成功", "Cookie刷新成功"))
                
                # 更新数据采集器的Cookie
                self.数据采集.cookie = new_cookie
            else:
                self.root.after(0, lambda: messagebox.showerror("错误", "Cookie刷新失败"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"Cookie刷新过程中出错: {e}"))
    
    def 显示关于信息(self):
        """显示关于信息"""
        messagebox.showinfo("关于", "闲鱼卖家商品采集工具\n版本: 2.0\n作者: AI助手\n\n功能：\n- 卖家商品采集\n- 数据查看与导出\n- AI游戏名称提纯")

    def 加载卖家数据(self):
        """加载所选卖家的数据"""
        选择项 = self.卖家选择.get()
        if not 选择项:
            messagebox.showerror("错误", "请先选择一个卖家")
            return
        
        # 从选择项中提取卖家ID
        卖家ID = 选择项.split("ID: ")[1].strip(")")
        
        # 清空表格
        self.清空数据表格()
        
        # 加载数据
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        if not os.path.exists(文件路径):
            messagebox.showerror("错误", f"找不到卖家 {卖家ID} 的数据文件")
            return
        
        数据 = self.数据处理.加载数据(文件路径)
        if "错误" in 数据:
            messagebox.showerror("错误", f"加载数据失败: {数据['错误']}")
            return
        
        # 显示卖家信息
        卖家信息 = 数据.get("卖家信息", {})
        商品列表 = 数据.get("商品列表", [])
        
        # 按想要人数排序（如果有）
        try:
            商品列表 = sorted(商品列表, key=lambda x: x.get("想要人数", 0), reverse=True)
        except:
            pass
        
        # 填充表格
        for i, 商品 in enumerate(商品列表):
            商品ID = 商品.get("商品ID", "")
            商品标题 = 商品.get("商品标题", "")
            想要人数 = 商品.get("想要人数", "")
            游戏名称 = 商品.get("游戏名称", "")
            首次采集时间 = 商品.get("首次采集时间", "")
            最近采集时间 = 商品.get("最近采集时间", "")
            
            self.数据表格.insert("", tk.END, values=(商品ID, 商品标题, 想要人数, 游戏名称, 首次采集时间, 最近采集时间))
        
        # 更新统计信息
        self.更新数据统计信息(商品列表, 卖家信息)
    
    def 更新数据统计信息(self, 商品列表, 卖家信息):
        """更新数据统计信息显示"""
        总商品数 = len(商品列表)
        有想要商品数 = sum(1 for 商品 in 商品列表 if 商品.get("想要人数", 0) > 0)
        无想要商品数 = 总商品数 - 有想要商品数
        
        想要数列表 = [int(商品.get("想要人数", 0)) for 商品 in 商品列表 if 商品.get("想要人数", 0) != ""]
        最高想要数 = max(想要数列表) if 想要数列表 else 0
        平均想要数 = round(sum(想要数列表) / len(想要数列表), 2) if 想要数列表 else 0
        
        最近采集时间 = 卖家信息.get("最近采集时间", "无")
        
        # 计算新上架商品数量（使用首次采集时间与最近采集时间相同的商品）
        新上架商品数 = sum(1 for 商品 in 商品列表 if 商品.get("首次采集时间", "") == 商品.get("最近采集时间", ""))
        
        # 更新标签
        self.总商品数标签.config(text=str(总商品数))
        self.有想要商品数标签.config(text=str(有想要商品数))
        self.无想要商品数标签.config(text=str(无想要商品数))
        self.最高想要数标签.config(text=str(最高想要数))
        self.平均想要数标签.config(text=str(平均想要数))
        self.最近采集时间标签.config(text=最近采集时间)
        self.新上架商品数标签.config(text=str(新上架商品数))
    
    def 清空数据表格(self):
        """清空数据表格"""
        for item in self.数据表格.get_children():
            self.数据表格.delete(item)
        
        # 重置统计信息
        self.总商品数标签.config(text="0")
        self.有想要商品数标签.config(text="0")
        self.无想要商品数标签.config(text="0")
        self.最高想要数标签.config(text="0")
        self.平均想要数标签.config(text="0")
        self.最近采集时间标签.config(text="无")
        self.新上架商品数标签.config(text="0")
    
    def 导出数据(self):
        """导出表格数据到CSV文件"""
        选择项 = self.卖家选择.get()
        if not 选择项:
            messagebox.showerror("错误", "请先选择一个卖家")
            return
        
        # 从选择项中提取卖家ID和名称
        卖家名称 = 选择项.split(" (ID:")[0]
        卖家ID = 选择项.split("ID: ")[1].strip(")")
        
        # 获取表格数据
        数据 = []
        for item in self.数据表格.get_children():
            values = self.数据表格.item(item, 'values')
            数据.append(values)
        
        if not 数据:
            messagebox.showerror("错误", "表格中没有数据可导出")
            return
        
        # 导出到CSV
        try:
            导出文件名 = f"卖家_{卖家ID}_{卖家名称}_导出.csv"
            with open(导出文件名, 'w', encoding='utf-8-sig', newline='') as f:
                import csv
                writer = csv.writer(f)
                writer.writerow(["商品ID", "商品标题", "想要人数", "游戏名称", "首次采集时间", "最近采集时间"])
                writer.writerows(数据)
            
            messagebox.showinfo("成功", f"数据已导出到文件: {导出文件名}")
            os.startfile(os.path.dirname(os.path.abspath(导出文件名)))
        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {e}")

    def 打开添加卖家窗口(self):
        """打开添加卖家窗口"""
        添加窗口 = tk.Toplevel(self.root)
        添加窗口.title("添加卖家")
        添加窗口.geometry("300x200")
        添加窗口.resizable(False, False)
        添加窗口.transient(self.root)
        添加窗口.grab_set()
        
        ttk.Label(添加窗口, text="卖家ID:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        卖家ID = tk.StringVar()
        ttk.Entry(添加窗口, textvariable=卖家ID, width=20).grid(row=0, column=1, padx=10, pady=10)
        
        ttk.Label(添加窗口, text="卖家名称:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        卖家名称 = tk.StringVar()
        ttk.Entry(添加窗口, textvariable=卖家名称, width=20).grid(row=1, column=1, padx=10, pady=10)
        
        ttk.Label(添加窗口, text="分组ID:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=10)
        分组ID = tk.StringVar(value="51959993")
        ttk.Entry(添加窗口, textvariable=分组ID, width=20).grid(row=2, column=1, padx=10, pady=10)
        
        ttk.Label(添加窗口, text="分组名称:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=10)
        分组名称 = tk.StringVar(value="综合")
        ttk.Entry(添加窗口, textvariable=分组名称, width=20).grid(row=3, column=1, padx=10, pady=10)
        
        def 确认添加():
            if not 卖家ID.get() or not 卖家名称.get():
                messagebox.showerror("错误", "卖家ID和卖家名称不能为空")
                return
                
            if self.配置.保存卖家信息(卖家ID.get(), 卖家名称.get()):
                messagebox.showinfo("成功", "添加卖家成功")
                self.刷新卖家列表()
                添加窗口.destroy()
            else:
                messagebox.showerror("错误", "添加卖家失败")
        
        ttk.Button(添加窗口, text="确认", command=确认添加).grid(row=4, column=1, sticky=tk.E, padx=10, pady=10)
    
    def 删除当前卖家(self):
        """删除当前卖家"""
        选择项 = self.卖家选择.get() if hasattr(self, '卖家选择') else None
        if not 选择项:
            messagebox.showerror("错误", "请先选择要删除的卖家")
            return
            
        # 从选择项中提取卖家ID和名称
        卖家名称 = 选择项.split(" (ID:")[0]
        卖家ID = 选择项.split("ID: ")[1].strip(")")
            
        if messagebox.askyesno("确认", f"确定要删除卖家 {卖家名称} (ID: {卖家ID}) 吗?"):
            if self.配置.删除卖家信息(卖家ID):
                messagebox.showinfo("成功", "删除卖家成功")
                self.刷新卖家列表()
                if hasattr(self, '提纯卖家选择'):
                    self.刷新提纯卖家列表()
            else:
                messagebox.showerror("错误", "删除卖家失败")
    
    def 设置为默认卖家(self, 卖家ID, 卖家名称):
        """设置为默认卖家"""
        if self.配置.更新默认设置(卖家ID, 卖家名称):
            messagebox.showinfo("成功", f"已将卖家 {卖家名称}({卖家ID}) 设置为默认卖家")
            self.默认设置 = self.配置.获取默认设置()
        else:
            messagebox.showerror("错误", "设置默认卖家失败")

    def 双击开始采集(self, event):
        """双击卖家列表项开始采集"""
        item = self.卖家列表视图.identify_row(event.y)
        if not item:
            return
            
        # 获取卖家信息
        values = self.卖家列表视图.item(item, "values")
        if not values:
            return
            
        卖家ID = values[1]
        self.开始单个卖家采集(卖家ID)

    def 标签页切换(self, event):
        """处理标签页切换事件"""
        当前标签 = self.选项卡.select()
        标签索引 = self.选项卡.index(当前标签)
        
        if 标签索引 == 1:  # 数据查看页面
            self.刷新卖家下拉列表()
        elif 标签索引 == 2:  # AI提纯页面
            self.刷新提纯卖家列表()

    def 保存提纯结果到JSON(self, 卖家ID, 提纯结果):
        """将AI提纯结果保存到JSON文件中"""
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        if not os.path.exists(文件路径):
            self.提纯状态标签.config(text=f"找不到卖家 {卖家ID} 的数据文件，无法保存提纯结果")
            return
        
        try:
            # 读取现有数据
            with open(文件路径, 'r', encoding='utf-8') as f:
                数据 = json.load(f)
            
            # 更新商品列表中的游戏名称
            for 商品 in 数据.get("商品列表", []):
                商品ID = 商品.get("商品ID", "")
                if 商品ID in 提纯结果:
                    商品["游戏名称"] = 提纯结果[商品ID]
            
            # 保存更新后的数据
            with open(文件路径, 'w', encoding='utf-8') as f:
                json.dump(数据, f, ensure_ascii=False, indent=2)
            
            self.提纯状态标签.config(text=f"提纯完成，并已将游戏名称保存到JSON文件")
        except Exception as e:
            self.提纯状态标签.config(text=f"保存提纯结果到JSON时出错: {e}")

    def 显示数据表格右键菜单(self, event):
        """显示数据表格右键菜单"""
        # 先获取点击位置的项
        item = self.数据表格.identify_row(event.y)
        if not item:
            return
            
        # 选中当前项
        self.数据表格.selection_set(item)
        
        # 创建右键菜单
        右键菜单 = tk.Menu(self.root, tearoff=0)
        
        # 添加菜单项
        右键菜单.add_command(label="复制商品ID", command=lambda: self.复制商品ID())
        右键菜单.add_command(label="复制商品标题", command=lambda: self.复制商品标题())
        右键菜单.add_command(label="复制游戏名称", command=lambda: self.复制游戏名称())
        右键菜单.add_separator()
        右键菜单.add_command(label="保存当前游戏名称", command=lambda: self.保存当前游戏名称())
        右键菜单.add_command(label="保存所有游戏名称", command=lambda: self.保存游戏名称到文件())
        右键菜单.add_separator()
        右键菜单.add_command(label="导出低想要商品ID", command=lambda: self.导出无想要商品ID())
        右键菜单.add_command(label="查看新上架游戏", command=lambda: self.打开新上架游戏文件())
        
        # 显示菜单
        try:
            右键菜单.tk_popup(event.x_root, event.y_root)
        finally:
            右键菜单.grab_release()
            
    def 复制商品ID(self):
        """复制选中商品的ID到剪贴板"""
        选中项 = self.数据表格.selection()
        if not 选中项:
            return
            
        商品ID = self.数据表格.item(选中项[0], 'values')[0]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品ID)
        
    def 复制商品标题(self):
        """复制选中商品的标题到剪贴板"""
        选中项 = self.数据表格.selection()
        if not 选中项:
            return
            
        商品标题 = self.数据表格.item(选中项[0], 'values')[1]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品标题)
        
    def 复制游戏名称(self):
        """复制选中商品的游戏名称到剪贴板"""
        选中项 = self.数据表格.selection()
        if not 选中项:
            return
            
        游戏名称 = self.数据表格.item(选中项[0], 'values')[3]
        if not 游戏名称:
            messagebox.showinfo("提示", "该商品没有游戏名称")
            return
            
        self.root.clipboard_clear()
        self.root.clipboard_append(游戏名称)
        
    def 保存当前游戏名称(self):
        """保存选中商品的游戏名称到文件"""
        选中项 = self.数据表格.selection()
        if not 选中项:
            return
            
        游戏名称 = self.数据表格.item(选中项[0], 'values')[3]
        if not 游戏名称:
            messagebox.showinfo("提示", "该商品没有游戏名称")
            return
            
        self.保存游戏名称到文件([游戏名称])
        
    def 保存游戏名称到文件(self, 游戏名称列表=None):
        """保存游戏名称到文件（追加模式，自动去重）"""
        if 游戏名称列表 is None:
            # 如果没有传入游戏名称列表，从表格中获取所有游戏名称
            游戏名称列表 = []
            for item in self.数据表格.get_children():
                游戏名称 = self.数据表格.item(item, 'values')[3]
                if 游戏名称 and 游戏名称.strip():  # 过滤空值
                    游戏名称列表.append(游戏名称)
                    
        if not 游戏名称列表:
            messagebox.showinfo("提示", "没有找到有效的游戏名称")
            return
        
        # 文件路径
        文件名 = "提纯游戏名字.txt"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        # 读取现有文件内容（如果存在）
        现有游戏名称 = set()
        if os.path.exists(文件路径):
            try:
                with open(文件路径, 'r', encoding='utf-8') as f:
                    for 行 in f:
                        名称 = 行.strip()
                        if 名称:
                            现有游戏名称.add(名称)
            except Exception as e:
                messagebox.showerror("错误", f"读取现有文件时出错: {e}")
                return
        
        # 去重并添加新的游戏名称
        新增数量 = 0
        for 名称 in 游戏名称列表:
            if 名称 not in 现有游戏名称:
                现有游戏名称.add(名称)
                新增数量 += 1
        
        # 保存到文件
        try:
            with open(文件路径, 'w', encoding='utf-8') as f:
                for 名称 in sorted(现有游戏名称):  # 排序后保存
                    f.write(f"{名称}\n")
            
            总数量 = len(现有游戏名称)
            messagebox.showinfo("成功", f"游戏名称已保存到文件: {文件名}\n新增: {新增数量} 个，总计: {总数量} 个")
        except Exception as e:
            messagebox.showerror("错误", f"保存游戏名称时出错: {e}")

    def 显示提纯表格右键菜单(self, event):
        """显示提纯表格右键菜单"""
        # 先获取点击位置的项
        item = self.提纯表格.identify_row(event.y)
        if not item:
            return
            
        # 选中当前项
        self.提纯表格.selection_set(item)
        
        # 创建右键菜单
        右键菜单 = tk.Menu(self.root, tearoff=0)
        
        # 添加菜单项
        右键菜单.add_command(label="复制商品ID", command=lambda: self.复制提纯商品ID())
        右键菜单.add_command(label="复制商品标题", command=lambda: self.复制提纯商品标题())
        右键菜单.add_command(label="复制游戏名称", command=lambda: self.复制提纯游戏名称())
        右键菜单.add_separator()
        右键菜单.add_command(label="保存当前游戏名称", command=lambda: self.保存当前提纯游戏名称())
        右键菜单.add_command(label="保存所有游戏名称", command=lambda: self.保存提纯游戏名称到文件())
        
        # 显示菜单
        try:
            右键菜单.tk_popup(event.x_root, event.y_root)
        finally:
            右键菜单.grab_release()
            
    def 复制提纯商品ID(self):
        """复制选中提纯商品的ID到剪贴板"""
        选中项 = self.提纯表格.selection()
        if not 选中项:
            return
            
        商品ID = self.提纯表格.item(选中项[0], 'values')[0]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品ID)
        
    def 复制提纯商品标题(self):
        """复制选中提纯商品的标题到剪贴板"""
        选中项 = self.提纯表格.selection()
        if not 选中项:
            return
            
        商品标题 = self.提纯表格.item(选中项[0], 'values')[1]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品标题)
        
    def 复制提纯游戏名称(self):
        """复制选中提纯商品的游戏名称到剪贴板"""
        选中项 = self.提纯表格.selection()
        if not 选中项:
            return
            
        游戏名称 = self.提纯表格.item(选中项[0], 'values')[3]
        if not 游戏名称:
            messagebox.showinfo("提示", "该商品没有游戏名称")
            return
            
        self.root.clipboard_clear()
        self.root.clipboard_append(游戏名称)
        
    def 保存当前提纯游戏名称(self):
        """保存选中提纯商品的游戏名称到文件"""
        选中项 = self.提纯表格.selection()
        if not 选中项:
            return
            
        游戏名称 = self.提纯表格.item(选中项[0], 'values')[3]
        if not 游戏名称:
            messagebox.showinfo("提示", "该商品没有游戏名称")
            return
            
        self.保存提纯游戏名称到文件([游戏名称])
        
    def 保存提纯游戏名称到文件(self, 游戏名称列表=None):
        """保存提纯页面的游戏名称到文件（追加模式，自动去重）"""
        if 游戏名称列表 is None:
            # 如果没有传入游戏名称列表，从表格中获取所有游戏名称
            游戏名称列表 = []
            for item in self.提纯表格.get_children():
                游戏名称 = self.提纯表格.item(item, 'values')[3]
                if 游戏名称 and 游戏名称.strip():  # 过滤空值
                    游戏名称列表.append(游戏名称)
                    
        if not 游戏名称列表:
            messagebox.showinfo("提示", "没有找到有效的游戏名称")
            return
        
        # 文件路径
        文件名 = "提纯游戏名字.txt"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        # 读取现有文件内容（如果存在）
        现有游戏名称 = set()
        if os.path.exists(文件路径):
            try:
                with open(文件路径, 'r', encoding='utf-8') as f:
                    for 行 in f:
                        名称 = 行.strip()
                        if 名称:
                            现有游戏名称.add(名称)
            except Exception as e:
                messagebox.showerror("错误", f"读取现有文件时出错: {e}")
                return
        
        # 去重并添加新的游戏名称
        新增数量 = 0
        for 名称 in 游戏名称列表:
            if 名称 not in 现有游戏名称:
                现有游戏名称.add(名称)
                新增数量 += 1
        
        # 保存到文件
        try:
            with open(文件路径, 'w', encoding='utf-8') as f:
                for 名称 in sorted(现有游戏名称):  # 排序后保存
                    f.write(f"{名称}\n")
            
            总数量 = len(现有游戏名称)
            messagebox.showinfo("成功", f"游戏名称已保存到文件: {文件名}\n新增: {新增数量} 个，总计: {总数量} 个")
        except Exception as e:
            messagebox.showerror("错误", f"保存游戏名称时出错: {e}")

    def 导出无想要商品ID(self):
        """导出无想要或想要数低于阈值的商品ID到TXT文件"""
        选择项 = self.卖家选择.get()
        if not 选择项:
            messagebox.showerror("错误", "请先选择一个卖家")
            return
        
        # 从选择项中提取卖家ID和名称
        卖家名称 = 选择项.split(" (ID:")[0]
        卖家ID = 选择项.split("ID: ")[1].strip(")")
        
        # 弹出对话框让用户输入想要数阈值
        阈值对话框 = tk.Toplevel(self.root)
        阈值对话框.title("设置想要数阈值")
        阈值对话框.geometry("300x120")
        阈值对话框.resizable(False, False)
        阈值对话框.transient(self.root)
        阈值对话框.grab_set()
        
        ttk.Label(阈值对话框, text="请输入想要数阈值（导出想要数小于等于该值的商品ID）:").pack(pady=(10, 5))
        
        阈值输入 = ttk.Spinbox(阈值对话框, from_=0, to=1000, width=10)
        阈值输入.set(0)  # 默认为0
        阈值输入.pack(pady=5)
        
        def 确认导出():
            try:
                阈值 = int(阈值输入.get())
                if 阈值 < 0:
                    messagebox.showerror("错误", "阈值不能为负数")
                    return
                
                # 获取表格数据中想要数小于等于阈值的商品ID
                符合条件商品ID列表 = []
                for item in self.数据表格.get_children():
                    values = self.数据表格.item(item, 'values')
                    商品ID = values[0]
                    想要人数 = values[2]
                    
                    # 检查想要人数是否小于等于阈值
                    try:
                        if 想要人数 == "" or int(想要人数) <= 阈值:
                            符合条件商品ID列表.append(商品ID)
                    except ValueError:
                        # 如果想要人数不是数字，则跳过
                        continue
                
                if not 符合条件商品ID列表:
                    messagebox.showinfo("提示", f"没有找到想要数小于等于{阈值}的商品")
                    阈值对话框.destroy()
                    return
                
                # 导出到TXT
                try:
                    导出文件名 = f"卖家_{卖家ID}_{卖家名称}_想要数小于等于{阈值}_商品ID.txt"
                    with open(导出文件名, 'w', encoding='utf-8') as f:
                        for 商品ID in 符合条件商品ID列表:
                            f.write(f"{商品ID}\n")
                    
                    messagebox.showinfo("成功", f"已导出{len(符合条件商品ID列表)}个想要数小于等于{阈值}的商品ID到文件: {导出文件名}")
                    os.startfile(os.path.dirname(os.path.abspath(导出文件名)))
                    阈值对话框.destroy()
                except Exception as e:
                    messagebox.showerror("错误", f"导出数据失败: {e}")
                    阈值对话框.destroy()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
        
        按钮框架 = ttk.Frame(阈值对话框)
        按钮框架.pack(fill=tk.X, pady=10)
        
        确认按钮 = ttk.Button(按钮框架, text="确认", command=确认导出)
        确认按钮.pack(side=tk.RIGHT, padx=10)
        
        取消按钮 = ttk.Button(按钮框架, text="取消", command=阈值对话框.destroy)
        取消按钮.pack(side=tk.RIGHT, padx=10)

    def 打开新上架游戏文件(self):
        """打开新上架游戏.txt文件"""
        文件名 = "新上架游戏.txt"
        文件路径 = os.path.join(os.path.dirname(os.path.abspath(__file__)), 文件名)
        
        if not os.path.exists(文件路径):
            with open(文件路径, 'w', encoding='utf-8') as f:
                pass  # 创建空文件
            messagebox.showinfo("提示", f"文件 {文件名} 不存在，已创建空文件")
        
        try:
            os.startfile(文件路径)
        except:
            try:
                import subprocess
                subprocess.run(['notepad', 文件路径])
            except:
                messagebox.showerror("错误", f"无法打开文件: {文件路径}")

    def 导入外部商品标题(self):
        """导入外部txt文件中的商品标题"""
        from tkinter import filedialog
        
        # 打开文件选择对话框
        文件路径 = filedialog.askopenfilename(
            title="选择商品标题文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if not 文件路径:
            return  # 用户取消了选择
        
        try:
            # 读取文件内容
            with open(文件路径, 'r', encoding='utf-8') as f:
                商品标题列表 = [line.strip() for line in f if line.strip()]
            
            if not 商品标题列表:
                messagebox.showinfo("提示", "所选文件不包含有效的商品标题")
                return
            
            # 清空现有表格
            self.清空提纯表格()
            
            # 导入标题到表格
            for i, 标题 in enumerate(商品标题列表):
                商品ID = f"外部导入_{i+1}"  # 生成一个临时ID
                self.提纯表格.insert("", tk.END, values=(商品ID, 标题, "", ""))
            
            self.提纯状态标签.config(text=f"已从外部文件导入 {len(商品标题列表)} 个商品标题")
            messagebox.showinfo("成功", f"已成功导入 {len(商品标题列表)} 个商品标题，可以点击\"开始AI提纯\"按钮进行提纯")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入文件时出错: {e}")

    def 创建新上架页面(self):
        """创建新上架商品页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.新上架页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格框架
        表格框架 = ttk.LabelFrame(主框架, text="新上架商品列表", padding="10")
        表格框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格和滚动条
        表格滚动条 = ttk.Scrollbar(表格框架)
        表格滚动条.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建水平滚动条
        水平滚动条 = ttk.Scrollbar(表格框架, orient=tk.HORIZONTAL)
        水平滚动条.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建表格
        self.新上架表格 = ttk.Treeview(表格框架, columns=("卖家名称", "卖家ID", "商品ID", "商品标题", "想要人数"), show="headings", height=20)
        self.新上架表格.pack(fill=tk.BOTH, expand=True)
        
        # 设置列宽和标题
        self.新上架表格.column("卖家名称", width=100, anchor=tk.W)
        self.新上架表格.column("卖家ID", width=100, anchor=tk.W)
        self.新上架表格.column("商品ID", width=100, anchor=tk.W)
        self.新上架表格.column("商品标题", width=400, anchor=tk.W)
        self.新上架表格.column("想要人数", width=80, anchor=tk.CENTER)
        
        self.新上架表格.heading("卖家名称", text="卖家名称")
        self.新上架表格.heading("卖家ID", text="卖家ID")
        self.新上架表格.heading("商品ID", text="商品ID")
        self.新上架表格.heading("商品标题", text="商品标题")
        self.新上架表格.heading("想要人数", text="想要人数")
        
        # 设置滚动条
        表格滚动条.config(command=self.新上架表格.yview)
        水平滚动条.config(command=self.新上架表格.xview)
        self.新上架表格.config(yscrollcommand=表格滚动条.set, xscrollcommand=水平滚动条.set)
        
        # 绑定右键菜单
        self.新上架表格.bind("<Button-3>", self.显示新上架表格右键菜单)
        
        # 创建操作按钮区域
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        导出按钮 = ttk.Button(按钮框架, text="导出新上架商品", command=self.导出新上架商品)
        导出按钮.pack(side=tk.LEFT, padx=5)
        
        清空按钮 = ttk.Button(按钮框架, text="清空列表", command=self.清空新上架表格)
        清空按钮.pack(side=tk.LEFT, padx=5)
        
        刷新按钮 = ttk.Button(按钮框架, text="刷新列表", command=self.刷新新上架表格)
        刷新按钮.pack(side=tk.LEFT, padx=5)
        
    def 显示新上架商品(self, 新上架商品列表):
        """显示新上架商品"""
        # 清空现有数据
        self.清空新上架表格()
        
        # 添加新数据
        for 商品 in 新上架商品列表:
            卖家ID = 商品.get('卖家ID', '未知')
            卖家名称 = 商品.get('卖家名称', '未知')
            商品ID = 商品.get('商品ID', '未知')
            商品标题 = 商品.get('商品标题', '未知')
            想要人数 = 商品.get('想要人数', 0)
            
            self.新上架表格.insert("", tk.END, values=(卖家名称, 卖家ID, 商品ID, 商品标题, 想要人数))
            
        # 切换到新上架商品标签页
        self.选项卡.select(self.新上架页面)
        
    def 清空新上架表格(self):
        """清空新上架商品表格"""
        for item in self.新上架表格.get_children():
            self.新上架表格.delete(item)
            
    def 刷新新上架表格(self):
        """刷新新上架商品表格"""
        # 获取当前表格中的数据
        数据 = []
        for item in self.新上架表格.get_children():
            values = self.新上架表格.item(item, "values")
            数据.append({
                "卖家名称": values[0],
                "卖家ID": values[1],
                "商品ID": values[2],
                "商品标题": values[3],
                "想要人数": int(values[4]) if values[4].isdigit() else 0
            })
            
        # 按想要人数排序
        数据.sort(key=lambda x: x["想要人数"], reverse=True)
        
        # 重新显示数据
        self.清空新上架表格()
        for 商品 in 数据:
            self.新上架表格.insert("", tk.END, values=(
                商品["卖家名称"],
                商品["卖家ID"],
                商品["商品ID"],
                商品["商品标题"],
                商品["想要人数"]
            ))
            
    def 导出新上架商品(self):
        """导出新上架商品到文件"""
        try:
            文件名 = f"新上架商品_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"
            with open(文件名, "w", encoding="utf-8") as f:
                for item in self.新上架表格.get_children():
                    values = self.新上架表格.item(item, "values")
                    卖家名称 = values[0]
                    卖家ID = values[1]
                    商品ID = values[2]
                    商品标题 = values[3]
                    想要人数 = values[4]
                    
                    f.write(f"卖家: {卖家名称}({卖家ID}) | 商品: {商品标题} | ID: {商品ID} | 想要人数: {想要人数}\n")
                    
            messagebox.showinfo("导出成功", f"新上架商品已导出到 {文件名}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出新上架商品时出错: {e}")
            
    def 显示新上架表格右键菜单(self, event):
        """显示新上架表格右键菜单"""
        # 获取当前选中的项
        item = self.新上架表格.identify_row(event.y)
        if not item:
            return
            
        # 选中该项
        self.新上架表格.selection_set(item)
        
        # 创建右键菜单
        菜单 = tk.Menu(self.root, tearoff=0)
        菜单.add_command(label="复制商品ID", command=self.复制新上架商品ID)
        菜单.add_command(label="复制商品标题", command=self.复制新上架商品标题)
        菜单.add_separator()
        菜单.add_command(label="在浏览器中打开", command=self.在浏览器中打开新上架商品)
        
        # 显示菜单
        菜单.post(event.x_root, event.y_root)
        
    def 复制新上架商品ID(self):
        """复制新上架商品ID到剪贴板"""
        选中项 = self.新上架表格.selection()
        if not 选中项:
            return
            
        商品ID = self.新上架表格.item(选中项[0], "values")[2]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品ID)
        messagebox.showinfo("复制成功", f"商品ID {商品ID} 已复制到剪贴板")
        
    def 复制新上架商品标题(self):
        """复制新上架商品标题到剪贴板"""
        选中项 = self.新上架表格.selection()
        if not 选中项:
            return
            
        商品标题 = self.新上架表格.item(选中项[0], "values")[3]
        self.root.clipboard_clear()
        self.root.clipboard_append(商品标题)
        messagebox.showinfo("复制成功", f"商品标题已复制到剪贴板")
        
    def 在浏览器中打开新上架商品(self):
        """在浏览器中打开新上架商品"""
        选中项 = self.新上架表格.selection()
        if not 选中项:
            return
            
        商品ID = self.新上架表格.item(选中项[0], "values")[2]
        url = f"https://2.taobao.com/item.htm?id={商品ID}"
        
        import webbrowser
        webbrowser.open(url)

def 创建界面(主程序):
    """创建图形用户界面"""
    root = tk.Tk()
    app = 闲鱼采集界面(root)
    app.主程序 = 主程序  # 保存主程序实例的引用
    
    # 初始化组件（使用主程序已经初始化好的组件）
    app.配置 = 主程序.配置管理器
    app.cookie管理 = 主程序.cookie管理器
    app.数据采集 = 主程序.数据采集器
    app.数据处理 = 主程序.数据处理器
    app.ai提纯器 = 主程序.ai提纯器
    
    # 获取配置信息
    app.默认设置 = app.配置.获取默认设置()

    # 更新界面中的变量
    app.当前卖家ID.set(app.默认设置.get("默认卖家id", ""))
    app.当前卖家名称.set(app.默认设置.get("默认卖家名称", ""))
    app.当前分组ID.set(app.默认设置.get("默认分组id", ""))
    app.当前分组名称.set(app.默认设置.get("默认分组名称", ""))

    # 刷新界面（这会获取卖家列表）
    app.刷新卖家列表()

    # 刷新设置页面（显示配置信息）
    app.刷新设置页面()

    # 刷新数据页面（加载卖家数据）
    app.刷新数据页面()

    # 刷新AI提纯页面（加载卖家数据）
    app.刷新AI提纯页面()

    # 添加更新状态方法，用于主程序调用
    def 更新状态(消息):
        app.写入日志(消息)
    
    # 将更新状态方法绑定到app对象上
    app.更新状态 = 更新状态
    
    return app

def 更新状态(消息):
    """更新状态（供主程序调用）"""
    print(消息)

def main():
    root = tk.Tk()
    app = 闲鱼采集界面(root)
    root.mainloop()
    
if __name__ == "__main__":
    main() 